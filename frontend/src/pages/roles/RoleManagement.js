import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  message,
  Tooltip,
  Tag,
  Row,
  Col,
  Collapse,
  InputNumber,
  Divider,
  Spin,
  Tabs,
  Checkbox,
  Empty,
  Badge,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  FunctionOutlined,
  AppstoreOutlined,
  CloudOutlined,
  CodeOutlined,
  EyeOutlined,
  RobotOutlined,
  DatabaseOutlined,
  LinkOutlined,
  DisconnectOutlined
} from '@ant-design/icons';

// 导入角色API
import { roleAPI } from '../../services/api/role';
import { modelConfigAPI } from '../../services/api/model';
import capabilityAPI from '../../services/api/capability';
import { settingsAPI } from '../../services/api/settings';
import externalKnowledgeAPI from '../../services/api/externalKnowledge';
import knowledgeAPI from '../../services/api/knowledge';
import { replaceTemplateVariables } from '../../utils/templateUtils';
import { getAssistantGenerationModelId } from '../../utils/modelUtils';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { confirm } = Modal;
const { TabPane } = Tabs;

const RoleManagement = () => {
  const [roles, setRoles] = useState([]);
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [form] = Form.useForm();
  const [testResult, setTestResult] = useState('');
  const [testVisible, setTestVisible] = useState(false);
  const [activeFormTab, setActiveFormTab] = useState('roleSettings');
  // 导入外部智能体相关状态
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importForm] = Form.useForm();
  const [importPlatform, setImportPlatform] = useState(null);

  // 能力相关状态
  const [capabilities, setCapabilities] = useState([]);
  const [loadingCapabilities, setLoadingCapabilities] = useState(false);
  const [selectedCapabilities, setSelectedCapabilities] = useState({});

  // 角色表格分页状态
  const [rolePagination, setRolePagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // 全局设置状态
  const [globalSettings, setGlobalSettings] = useState({
    streamingEnabled: true // 默认启用流式响应
  });

  // 知识库相关状态
  const [allKnowledges, setAllKnowledges] = useState([]); // 所有知识库（内部+外部）
  const [roleKnowledges, setRoleKnowledges] = useState([]);
  const [loadingKnowledges, setLoadingKnowledges] = useState(false);
  const [selectedKnowledges, setSelectedKnowledges] = useState([]);

  // 辅助生成相关状态
  const [assistantGenerating, setAssistantGenerating] = useState(false);

  // 获取角色列表和模型列表
  useEffect(() => {
    fetchRoles();
    fetchModels();
    fetchCapabilities();
    fetchGlobalSettings();
    fetchAllKnowledges();
  }, []);

  const fetchRoles = async () => {
    setLoading(true);
    try {
      // 调用实际API获取数据
      console.log('开始获取角色列表...');
      const data = await roleAPI.getAll();
      console.log('获取到角色数据:', data);
      setRoles(data);
    } catch (error) {
      console.error('获取角色列表失败:', error);
      console.error('错误详情:', error.response?.data || error.message);
      message.error(`获取角色列表失败: ${error.message || '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchModels = async () => {
    try {
      setLoadingModels(true);
      console.log('开始获取模型列表...');
      // 使用modelConfigAPI.getAll获取模型配置
      const data = await modelConfigAPI.getAll();
      console.log('获取到模型数据:', data);
      // 确保所有模型数据字段完整
      if (data && Array.isArray(data)) {
        data.forEach(model => {
          console.log(`模型 ${model.name} (ID: ${model.id}) - base_url: ${model.base_url || '未设置'}`);
        });
      }
      setModels(data);
    } catch (error) {
      console.error('获取模型列表失败:', error);
      console.error('错误详情:', error.response?.data || error.message);
      message.error(`获取模型列表失败: ${error.message || '未知错误'}`);
    } finally {
      setLoadingModels(false);
    }
  };

  const fetchCapabilities = async () => {
    try {
      setLoadingCapabilities(true);
      const response = await capabilityAPI.getAll();
      console.log('获取到能力数据:', response);

      let capabilitiesData = [];
      if (response.data && Array.isArray(response.data)) {
        capabilitiesData = response.data;
      }

      // 按类型组织能力
      const capsByType = {};
      capabilitiesData.forEach(cap => {
        if (!capsByType[cap.type]) {
          capsByType[cap.type] = [];
        }
        capsByType[cap.type].push(cap);
      });

      setCapabilities(capsByType);
    } catch (error) {
      console.error('获取能力列表失败:', error);
      message.error(`获取能力列表失败: ${error.message || '未知错误'}`);
    } finally {
      setLoadingCapabilities(false);
    }
  };

  const fetchGlobalSettings = async () => {
    try {
      const settings = await settingsAPI.getSettings();
      console.log('获取到全局设置:', settings);
      setGlobalSettings({
        streamingEnabled: settings.streamingEnabled !== undefined ? settings.streamingEnabled : true,
        enableAssistantGeneration: settings.enableAssistantGeneration !== undefined ? settings.enableAssistantGeneration : true,
        assistantGenerationModel: settings.assistantGenerationModel || 'default'
      });
    } catch (error) {
      console.error('获取全局设置失败:', error);
      // 使用默认值，不显示错误消息
    }
  };

  // 获取所有知识库列表（内部+外部）
  const fetchAllKnowledges = async () => {
    try {
      setLoadingKnowledges(true);

      // 并行获取内部知识库和外部知识库
      const [internalResponse, externalResponse] = await Promise.allSettled([
        knowledgeAPI.getAll(),
        externalKnowledgeAPI.getExternalKnowledges()
      ]);

      console.log('获取到内部知识库数据:', internalResponse);
      console.log('获取到外部知识库数据:', externalResponse);

      let allKnowledgesData = [];

      // 处理内部知识库数据
      if (internalResponse.status === 'fulfilled') {
        let internalKnowledges = [];
        const internalData = internalResponse.value;

        if (internalData.data && Array.isArray(internalData.data)) {
          internalKnowledges = internalData.data;
        } else if (Array.isArray(internalData)) {
          internalKnowledges = internalData;
        }

        // 为内部知识库添加类型标识
        internalKnowledges = internalKnowledges.map(kb => ({
          ...kb,
          type: 'internal',
          provider_name: '内部知识库',
          status: 'active' // 内部知识库默认为活跃状态
        }));

        allKnowledgesData = [...allKnowledgesData, ...internalKnowledges];
      } else {
        console.error('获取内部知识库失败:', internalResponse.reason);
      }

      // 处理外部知识库数据
      if (externalResponse.status === 'fulfilled') {
        let externalKnowledges = [];
        const externalData = externalResponse.value;

        if (externalData.data && Array.isArray(externalData.data)) {
          externalKnowledges = externalData.data;
        } else if (Array.isArray(externalData)) {
          externalKnowledges = externalData;
        }

        // 为外部知识库添加类型标识
        externalKnowledges = externalKnowledges.map(kb => ({
          ...kb,
          type: 'external'
        }));

        allKnowledgesData = [...allKnowledgesData, ...externalKnowledges];
      } else {
        console.error('获取外部知识库失败:', externalResponse.reason);
      }

      console.log('合并后的知识库数据:', allKnowledgesData);
      setAllKnowledges(allKnowledgesData);

    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error(`获取知识库列表失败: ${error.message || '未知错误'}`);
    } finally {
      setLoadingKnowledges(false);
    }
  };

  // 获取角色绑定的知识库（内部+外部）
  const fetchRoleKnowledges = async (roleId) => {
    if (!roleId) return;

    try {
      setLoadingKnowledges(true);

      // 并行获取内部知识库绑定和外部知识库绑定
      const [internalResponse, externalResponse] = await Promise.allSettled([
        knowledgeAPI.getRoleKnowledges(roleId),
        externalKnowledgeAPI.getRoleExternalKnowledges(roleId)
      ]);

      console.log('获取到角色内部知识库绑定数据:', internalResponse);
      console.log('获取到角色外部知识库绑定数据:', externalResponse);

      let allRoleKnowledges = [];
      let selectedKnowledgeIds = [];

      // 处理内部知识库绑定
      if (internalResponse.status === 'fulfilled') {
        let internalKnowledges = [];
        const internalData = internalResponse.value;

        if (internalData.data && Array.isArray(internalData.data)) {
          internalKnowledges = internalData.data;
        } else if (Array.isArray(internalData)) {
          internalKnowledges = internalData;
        }

        // 为内部知识库添加类型标识和前缀
        internalKnowledges = internalKnowledges.map(kb => ({
          ...kb,
          type: 'internal',
          original_id: kb.id, // 保存原始ID
          id: `internal_${kb.id}` // 添加前缀避免与外部知识库ID冲突
        }));

        allRoleKnowledges = [...allRoleKnowledges, ...internalKnowledges];
        selectedKnowledgeIds = [...selectedKnowledgeIds, ...internalKnowledges.map(kb => kb.id)];
      } else {
        console.error('获取角色内部知识库绑定失败:', internalResponse.reason);
      }

      // 处理外部知识库绑定
      if (externalResponse.status === 'fulfilled') {
        let externalKnowledges = [];
        const externalData = externalResponse.value;

        if (externalData.data && Array.isArray(externalData.data)) {
          externalKnowledges = externalData.data;
        } else if (Array.isArray(externalData)) {
          externalKnowledges = externalData;
        }

        // 为外部知识库添加类型标识和前缀
        externalKnowledges = externalKnowledges.map(binding => ({
          ...binding.knowledge, // 使用知识库的信息
          type: 'external',
          original_id: binding.knowledge.id, // 保存知识库的原始ID
          id: `external_${binding.knowledge.id}`, // 使用知识库ID添加前缀
          binding_id: binding.id, // 保存绑定关系ID，用于解绑时使用
          provider_name: binding.provider ? binding.provider.name : '未知提供商'
        }));

        allRoleKnowledges = [...allRoleKnowledges, ...externalKnowledges];
        selectedKnowledgeIds = [...selectedKnowledgeIds, ...externalKnowledges.map(kb => kb.id)];
      } else {
        console.error('获取角色外部知识库绑定失败:', externalResponse.reason);
      }

      console.log('合并后的角色知识库绑定数据:', allRoleKnowledges);
      setRoleKnowledges(allRoleKnowledges);
      setSelectedKnowledges(selectedKnowledgeIds);

    } catch (error) {
      console.error('获取角色知识库绑定失败:', error);
      message.error(`获取角色知识库绑定失败: ${error.message || '未知错误'}`);
    } finally {
      setLoadingKnowledges(false);
    }
  };

  // 辅助生成系统提示词
  const handleAssistantGenerate = async () => {
    try {
      // 检查是否启用了辅助生成
      if (!globalSettings.enableAssistantGeneration) {
        message.warning('辅助生成功能未启用，请在系统设置中开启');
        return;
      }

      // 获取当前表单的角色名称和描述
      const values = form.getFieldsValue(['name', 'description']);

      if (!values.name || !values.description) {
        message.warning('请先填写角色名称和描述，然后再使用辅助生成');
        return;
      }

      setAssistantGenerating(true);

      // 获取系统设置的提示词模板
      let promptTemplate;
      try {
        const templates = await settingsAPI.getPromptTemplates();
        promptTemplate = templates.roleSystemPrompt;
      } catch (error) {
        console.error('获取提示词模板失败，使用默认模板:', error);
        // 使用默认模板
        promptTemplate = `请根据以下角色信息生成一个专业的系统提示词：

角色名称：{{name}}
角色描述：{{description}}

要求：
1. 系统提示词应该清晰地定义角色的身份、专业领域和能力
2. 包含角色的行为准则和回答风格
3. 明确角色的职责范围和限制
4. 使用专业、准确的语言
5. 长度适中，既要详细又要简洁

请直接返回系统提示词内容，不需要额外的解释。`;
      }

      // 使用模板变量替换功能
      const generatePrompt = replaceTemplateVariables(promptTemplate, {
        name: values.name,
        description: values.description
      });

      // 确定使用的模型
      const modelToUse = await getAssistantGenerationModelId(models, globalSettings.assistantGenerationModel);

      // 调用模型API生成提示词
      let generatedPrompt = '';
      const handleStreamResponse = (chunk) => {
        // 过滤掉null、undefined和空字符串
        if (chunk && chunk !== 'null' && chunk !== 'undefined' && typeof chunk === 'string') {
          generatedPrompt += chunk;
          // 实时更新表单中的系统提示词字段
          form.setFieldsValue({
            systemPrompt: generatedPrompt
          });
        }
      };

      await modelConfigAPI.testModelStream(
        modelToUse,
        generatePrompt,
        handleStreamResponse,
        "你是一个专业的AI提示词工程师，擅长根据角色描述生成高质量的系统提示词。",
        {
          temperature: 0.7,
          max_tokens: 1000
        }
      );

      // 最终清理生成的内容，移除可能的null字符串
      const cleanedPrompt = generatedPrompt
        .replace(/null/g, '')
        .replace(/undefined/g, '')
        .trim();

      form.setFieldsValue({
        systemPrompt: cleanedPrompt
      });

      message.success('系统提示词生成完成');
    } catch (error) {
      console.error('辅助生成失败:', error);
      message.error(`辅助生成失败: ${error.message || '未知错误'}`);
    } finally {
      setAssistantGenerating(false);
    }
  };

  const showAddModal = () => {
    setSelectedRole(null);
    form.resetFields();
    form.setFieldsValue({
      source: 'internal', // 固化为内部角色类型
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      maxTokens: 2000,
      stopSequences: [],
      // 默认的能力设置
      capabilities: {
        function_calling: true,
        agent_coordination: true,
        tool_use: true,
        code_execution: false,
        web_browsing: false,
        external_api: false
      },
      // 默认不选择任何插件
      plugins: []
    });
    setTestResult('');
    setTestVisible(false);
    setModalVisible(true);
    setActiveFormTab('roleSettings');
    // 清理知识库相关状态
    setRoleKnowledges([]);
    setSelectedKnowledges([]);
  };

  const showEditModal = (role) => {
    console.log('编辑角色:', role);
    console.log('可用模型列表:', models);

    setSelectedRole(role);

    // 如果是外部角色，使用导入表单进行编辑
    if (role.source === 'external') {
      showEditExternalModal(role);
      return;
    }

    // 内部角色的编辑逻辑
    // 处理模型设置：如果角色的model为null或undefined，表示使用默认模型
    let modelToUse = role.model;

    if (role.model === null || role.model === undefined) {
      // 角色使用默认模型，在表单中设置为null
      modelToUse = null;
      console.log('角色使用默认模型');
    } else {
      // 检查角色的model是否存在于可用模型中
      const modelExists = models.some(m => m.id.toString() === role.model?.toString());
      console.log('模型是否存在于可用列表中:', modelExists, '角色模型ID:', role.model);

      // 如果模型不存在，尝试使用默认模型
      if (!modelExists && models.length > 0) {
        const defaultModel = models.find(m => m.is_default_text) || models.find(m => m.is_default) || models[0];
        console.log('使用默认/第一个模型代替:', defaultModel.id);
        modelToUse = defaultModel.id;
      }
    }

    // 获取角色的能力和工具
    const fetchRoleCapabilitiesAndTools = async () => {
      try {
        // 获取角色能力
        const capabilitiesResponse = await capabilityAPI.getByRoleId(role.id);
        console.log("编辑模式 - 获取到角色能力:", capabilitiesResponse);

        let roleCapabilities = [];
        // 确保capabilitiesResponse.data是数组
        if (capabilitiesResponse && capabilitiesResponse.data) {
          if (Array.isArray(capabilitiesResponse.data)) {
            roleCapabilities = capabilitiesResponse.data;
          } else if (typeof capabilitiesResponse.data === 'object' &&
                    capabilitiesResponse.data.status === 'success' &&
                    Array.isArray(capabilitiesResponse.data.data)) {
            roleCapabilities = capabilitiesResponse.data.data;
          }
        }

        const capabilitiesMap = {};
        roleCapabilities.forEach(cap => {
          capabilitiesMap[cap.id] = true;
        });
        setSelectedCapabilities(capabilitiesMap);


      } catch (error) {
        console.error('获取角色能力和工具失败:', error);
      }
    };

    if (role.id) {
      fetchRoleCapabilitiesAndTools();
    }

    form.setFieldsValue({
      name: role.name,
      model: modelToUse, // 使用检查后的模型ID
      systemPrompt: role.system_prompt,
      description: role.description,
      source: role.source || 'internal', // 设置角色类型
      temperature: role.temperature || 0.7,
      topP: role.topP || 1,
      frequencyPenalty: role.frequencyPenalty || 0,
      presencePenalty: role.presencePenalty || 0,
      stopSequences: role.stopSequences || [],
      // 设置能力和插件
      capabilities: role.capabilities || {
        function_calling: true,
        agent_coordination: true,
        tool_use: true,
        code_execution: false,
        web_browsing: false,
        external_api: false
      },
      plugins: role.plugins || []
    });
    setTestResult('');
    setTestVisible(false);
    setModalVisible(true);
    setActiveFormTab('roleSettings');

    // 获取角色绑定的知识库
    fetchRoleKnowledges(role.id);
  };

  const showEditExternalModal = (role) => {
    // 设置外部角色编辑状态
    setImportModalVisible(true);

    console.log('编辑外部角色，原始角色数据:', role);
    console.log('角色settings:', role.settings);
    console.log('角色external_config:', role.external_config);

    // 从角色的外部配置中提取数据
    // 检查两个可能的位置：role.settings.external_config 和 role.external_config
    const externalConfig = role.external_config || role.settings?.external_config || {};
    const apiConfig = externalConfig.api_config || {};
    const platformSpecific = externalConfig.platform_specific || {};

    console.log('解析的外部配置:', externalConfig);
    console.log('解析的API配置:', apiConfig);
    console.log('解析的平台特定配置:', platformSpecific);

    // 设置平台类型状态
    const platform = externalConfig.platform || role.external_type || 'custom';
    setImportPlatform(platform);

    // 填充表单数据
    const formData = {
      name: role.name,
      description: role.description,
      source: 'external',
      platform: platform,
      // API配置
      apiKey: apiConfig.api_key || '',
      apiServer: apiConfig.base_url || '',
      model: apiConfig.model || '',
      timeout: apiConfig.timeout || 60,
      // 平台特定配置
      instructions: platformSpecific.instructions || '',
      applicationType: platformSpecific.application_type || '',
      platformName: platformSpecific.platform_name || '',
      // 外部ID
      assistantId: externalConfig.external_id || role.external_id || '',
      botId: externalConfig.external_id || role.external_id || '',
      // 高级配置 - 基于全局流式响应配置设置默认值
      responseMode: apiConfig.response_mode || (globalSettings.streamingEnabled ? 'streaming' : 'blocking'),
      userIdentifier: apiConfig.user_identifier || '',
      // 自定义请求头
      headers: apiConfig.headers ? JSON.stringify(apiConfig.headers, null, 2) : ''
    };

    console.log('编辑外部角色，填充数据:', formData);
    importForm.setFieldsValue(formData);
  };

  const handleDelete = (role) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除角色 "${role.name}" 吗？`,
      onOk: async () => {
        try {
          await roleAPI.delete(role.id);
          message.success('角色删除成功');
          fetchRoles(); // 重新获取数据
        } catch (error) {
          console.error('删除角色失败:', error);
          message.error('删除角色失败');
        }
      },
    });
  };

  // 处理知识库绑定变更
  const handleKnowledgeBindingChange = async (roleId, newSelectedKnowledges) => {
    try {
      const currentKnowledgeIds = roleKnowledges.map(kb => kb.id);
      const newKnowledgeIds = newSelectedKnowledges;

      // 找出需要绑定的知识库（新选择的）
      const toBindIds = newKnowledgeIds.filter(id => !currentKnowledgeIds.includes(id));

      // 找出需要解绑的知识库（取消选择的）
      const toUnbindIds = currentKnowledgeIds.filter(id => !newKnowledgeIds.includes(id));

      // 分别处理内部和外部知识库的绑定/解绑
      const internalToBindIds = toBindIds.filter(id => id.startsWith('internal_')).map(id => id.replace('internal_', ''));
      const externalToBindIds = toBindIds.filter(id => id.startsWith('external_')).map(id => id.replace('external_', ''));
      const internalToUnbindIds = toUnbindIds.filter(id => id.startsWith('internal_')).map(id => id.replace('internal_', ''));
      const externalToUnbindIds = toUnbindIds.filter(id => id.startsWith('external_')).map(id => id.replace('external_', ''));

      // 执行内部知识库绑定操作
      for (const kbId of internalToBindIds) {
        try {
          console.log(`绑定内部知识库: roleId=${roleId}, kbId=${kbId}`);
          await knowledgeAPI.mountToRole(roleId, kbId);
          console.log(`内部知识库绑定成功: ${kbId}`);
        } catch (error) {
          console.error(`内部知识库绑定失败: ${kbId}`, error);
          throw error;
        }
      }

      // 执行外部知识库绑定操作
      for (const kbId of externalToBindIds) {
        try {
          console.log(`绑定外部知识库: roleId=${roleId}, kbId=${kbId}`);
          await externalKnowledgeAPI.bindRoleExternalKnowledge(roleId, kbId);
          console.log(`外部知识库绑定成功: ${kbId}`);
        } catch (error) {
          console.error(`外部知识库绑定失败: ${kbId}`, error);
          throw error;
        }
      }

      // 执行内部知识库解绑操作
      for (const kbId of internalToUnbindIds) {
        try {
          console.log(`解绑内部知识库: roleId=${roleId}, kbId=${kbId}`);
          await knowledgeAPI.unmountFromRole(roleId, kbId);
          console.log(`内部知识库解绑成功: ${kbId}`);
        } catch (error) {
          console.error(`内部知识库解绑失败: ${kbId}`, error);
          throw error;
        }
      }

      // 执行外部知识库解绑操作
      for (const kbId of externalToUnbindIds) {
        try {
          console.log(`解绑外部知识库: roleId=${roleId}, kbId=${kbId}`);
          await externalKnowledgeAPI.unbindRoleExternalKnowledge(roleId, kbId);
          console.log(`外部知识库解绑成功: ${kbId}`);
        } catch (error) {
          console.error(`外部知识库解绑失败: ${kbId}`, error);
          throw error;
        }
      }

      // 刷新角色知识库绑定数据
      await fetchRoleKnowledges(roleId);

      if (toBindIds.length > 0 || toUnbindIds.length > 0) {
        message.success('知识库绑定更新成功');
      }
    } catch (error) {
      console.error('更新知识库绑定失败:', error);
      message.error(`更新知识库绑定失败: ${error.message || '未知错误'}`);
      // 恢复之前的选择状态
      setSelectedKnowledges(roleKnowledges.map(kb => kb.id));
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      // 打印表单数据，调试用
      console.log('表单数据:', values);

      // 准备与API兼容的数据格式
      const formData = {
        name: values.name,
        model: values.model,
        system_prompt: values.systemPrompt,
        description: values.description,
        source: values.source || 'internal',  // 添加角色类型字段
        temperature: values.temperature,
        topP: values.topP || 1,
        frequencyPenalty: values.frequencyPenalty || 0,
        presencePenalty: values.presencePenalty || 0,
        stopSequences: values.stopSequences || [],
        capabilities: values.capabilities || {
          function_calling: true,
          agent_coordination: true,
          tool_use: true
        }
      };

      // 打印将要发送到API的数据，调试用
      console.log('发送到API的数据:', formData);

      if (selectedRole) {
        // 更新现有角色
        await roleAPI.update(selectedRole.id, formData);

        // 更新角色能力关联
        await updateRoleCapabilities(selectedRole.id, selectedCapabilities);

        // 更新知识库绑定
        await handleKnowledgeBindingChange(selectedRole.id, selectedKnowledges);

        message.success('角色更新成功');
      } else {
        // 创建新角色
        const result = await roleAPI.create(formData);

        if (result && result.data && result.data.id) {
          const newRoleId = result.data.id;

          // 为新角色添加能力
          await updateRoleCapabilities(newRoleId, selectedCapabilities);

          // 为新角色绑定知识库
          if (selectedKnowledges.length > 0) {
            // 分别处理内部和外部知识库的绑定
            const internalKnowledgeIds = selectedKnowledges.filter(id => id.startsWith('internal_')).map(id => id.replace('internal_', ''));
            const externalKnowledgeIds = selectedKnowledges.filter(id => id.startsWith('external_')).map(id => id.replace('external_', ''));

            // 绑定内部知识库
            for (const kbId of internalKnowledgeIds) {
              try {
                console.log(`为新角色绑定内部知识库: roleId=${newRoleId}, kbId=${kbId}`);
                await knowledgeAPI.mountToRole(newRoleId, kbId);
                console.log(`新角色内部知识库绑定成功: ${kbId}`);
              } catch (error) {
                console.error(`新角色内部知识库绑定失败: ${kbId}`, error);
                // 对于新角色，绑定失败不应该阻止整个创建过程，只记录错误
              }
            }

            // 绑定外部知识库
            for (const kbId of externalKnowledgeIds) {
              try {
                console.log(`为新角色绑定外部知识库: roleId=${newRoleId}, kbId=${kbId}`);
                await externalKnowledgeAPI.bindRoleExternalKnowledge(newRoleId, kbId);
                console.log(`新角色外部知识库绑定成功: ${kbId}`);
              } catch (error) {
                console.error(`新角色外部知识库绑定失败: ${kbId}`, error);
                // 对于新角色，绑定失败不应该阻止整个创建过程，只记录错误
              }
            }
          }
        }

        message.success('角色创建成功');
      }

      setModalVisible(false);
      fetchRoles(); // 重新获取角色列表
    } catch (error) {
      console.error('保存角色失败:', error);
      message.error('保存角色失败，请检查表单内容');
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    setTestResult('');
    setTestVisible(false);
    // 清理知识库相关状态
    setRoleKnowledges([]);
    setSelectedKnowledges([]);
  };



  const getModelBadge = (model) => {
    const modelColors = {
      'gpt-4': 'cyan',
      'gpt-3.5-turbo': 'blue',
      'claude-3-opus': 'purple',
      'claude-3-sonnet': 'geekblue',
      'gemini-pro': 'green',
      'llama-3': 'orange',
    };
    return modelColors[model] || 'default';
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Space>
          <UserOutlined style={{ color: '#1677ff' }} />
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'source',
      key: 'source',
      render: (type) => {
        const typeColors = {
          'internal': 'blue',
          'external': 'green'
        };
        const typeLabels = {
          'internal': '内部',
          'external': '外部'
        };
        return <Tag color={typeColors[type] || 'blue'}>{typeLabels[type] || type || '内部'}</Tag>;
      },
    },
    {
      title: '使用的模型',
      dataIndex: 'model',
      key: 'model',
      render: (model, record) => {
        // 如果是外部角色，显示平台类型
        if (record.source === 'external') {
          const platformType = record.external_type || 'custom';
          const platformColors = {
            'openai': 'blue',
            'dify': 'green',
            'coze': 'purple',
            'custom': 'orange'
          };
          const platformLabels = {
            'openai': 'OpenAI',
            'dify': 'Dify',
            'coze': 'Coze',
            'custom': '自定义'
          };
          return (
            <Tag color={platformColors[platformType] || 'orange'}>
              {platformLabels[platformType] || platformType}
            </Tag>
          );
        }

        // 内部角色显示模型信息
        if (model === null || model === undefined) {
          // 使用默认模型
          const defaultModel = models.find(m => m.is_default_text) || models.find(m => m.is_default);
          return (
            <Tag color={getModelBadge(defaultModel?.model_id)}>
              默认文本生成 {defaultModel ? `(${defaultModel.name})` : ''}
            </Tag>
          );
        }

        const modelConfig = models.find(m => m.id.toString() === model?.toString());
        return <Tag color={getModelBadge(modelConfig?.model_id)}>{record.model_name || modelConfig?.name || '默认'}</Tag>;
      },
    },
    {
      title: '系统提示词',
      dataIndex: 'systemPrompt',
      key: 'systemPrompt',
      ellipsis: {
        showTitle: false,
      },
      render: (_, record) => (
        <Tooltip placement="topLeft" title={record.system_prompt || '无提示词'}>
          <div style={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {record.system_prompt ? record.system_prompt.substring(0, 50) + (record.system_prompt.length > 50 ? '...' : '') : '无提示词'}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (description) => (
        <Tooltip placement="topLeft" title={description}>
          <div style={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {description}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
            style={{ color: '#1677ff' }}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    },
  ];

  const handleTestLLM = async () => {
    try {
      const values = await form.validateFields();

      // 检查是否选择了模型，如果没有选择，显示错误提示
      if (values.model === undefined) {
        message.error('请先选择一个模型');
        return;
      }

      setTestResult('');
      setTestVisible(true);

      try {
        // 准备角色级别的模型参数（max_tokens从模型配置中获取）
        const advancedParams = {
          system_prompt: values.systemPrompt,
          temperature: values.temperature,
          top_p: values.topP,
          frequency_penalty: values.frequencyPenalty,
          presence_penalty: values.presencePenalty,
          stop_sequences: values.stopSequences
        };

        // 通用流式响应处理函数
        let streamContent = '';
        const handleStreamResponse = (chunk) => {
          if (chunk) {
            streamContent += chunk;
            setTestResult(streamContent);
          }
        };

        // 处理模型选择：如果选择了默认模型（null），则使用默认文本生成模型
        let selectedModelConfig;
        if (values.model === null) {
          // 选择了默认模型
          selectedModelConfig = models.find(m => m.is_default_text) ||
                               models.find(m => m.modalities && m.modalities.includes('text_output'));
          if (!selectedModelConfig) {
            throw new Error('未找到默认文本生成模型配置，请在模型配置中设置默认文本生成模型');
          }
          console.log('使用默认文本生成模型:', selectedModelConfig.name);
        } else {
          // 选择了具体模型
          selectedModelConfig = models.find(m => m.id.toString() === values.model?.toString());
          if (!selectedModelConfig) {
            // 如果找不到所选模型，尝试使用默认文本生成模型
            const defaultModel = models.find(m => m.is_default_text) ||
                                 models.find(m => m.modalities && m.modalities.includes('text_output'));
            if (defaultModel) {
              console.log('所选模型未找到，使用默认文本生成模型:', defaultModel.name);
              selectedModelConfig = defaultModel;
            } else {
              throw new Error('未找到所选模型配置，请确保已选择有效的模型');
            }
          }
        }

        console.log('选中的模型配置:', {
          ...selectedModelConfig,
          api_key: selectedModelConfig.api_key ? '***已隐藏***' : undefined
        });

        // 检查模型配置中的URL，支持多种字段名称
        const baseUrl = selectedModelConfig.base_url ||
                       selectedModelConfig.baseUrl ||
                       selectedModelConfig.url ||
                       selectedModelConfig.endpoint ||
                       '';

        // 如果从角色模型列表中获取的配置没有URL，尝试直接从模型API获取完整配置
        if (!baseUrl) {
          try {
            console.log('尝试直接从模型API获取完整配置...');
            const completeConfig = await modelConfigAPI.getModelById(selectedModelConfig.id);

            if (completeConfig && (completeConfig.base_url || completeConfig.baseUrl)) {
              console.log('成功从模型API获取完整配置:', completeConfig);

              // 使用流式API测试
              await modelConfigAPI.testModelStream(
                completeConfig.id,
                "请简单地介绍一下你自己。",
                handleStreamResponse,
                values.systemPrompt,
                advancedParams
              );

              return;
            } else {
              console.error('从模型API获取的配置仍然缺少URL:', completeConfig);
            }
          } catch (configError) {
            console.error('直接获取模型配置失败:', configError);
          }

          throw new Error('模型配置中没有找到基础URL，请确保已正确设置');
        }

        // 使用流式API测试
        await modelConfigAPI.testModelStream(
          selectedModelConfig.id,
          "请简单地介绍一下你自己。",
          handleStreamResponse,
          values.systemPrompt,
          advancedParams
        );

      } catch (error) {
        console.error('测试LLM失败:', error);
        setTestResult(`测试失败: ${error.message || '未知错误'}`);
      }
    } catch (error) {
      message.error('请先完成表单填写');
    }
  };

  const showImportModal = () => {
    setImportModalVisible(true);
    importForm.resetFields();
    setImportPlatform(null);
  };

  const handleImportModalCancel = () => {
    setImportModalVisible(false);
    // 重置表单和状态
    importForm.resetFields();
    setImportPlatform(null);
    setSelectedRole(null); // 清除选中的角色，确保下次是创建模式
    // 清除测试连接结果
    setTestConnectionLoading(false);
    setTestConnectionResult(null);
  };

  const handleImportModalOk = async () => {
    try {
      const values = await importForm.validateFields();
      console.log('导入外部智能体表单数据:', values);

      // 构建API参数
      const apiValues = {
        name: values.name,
        description: values.description,
        source: 'external',  // 设置为外部角色类型
        external_type: values.platform,
        external_id: values.assistantId || values.botId || '',
        external_config: {
          api_key: values.apiKey,
          model: values.model,
          application_type: values.applicationType,
          base_url: values.apiServer,
          instructions: values.instructions,
          timeout: values.timeout || 60,
          response_mode: values.responseMode || 'blocking',
          user_identifier: values.userIdentifier || ''
        }
      };

      // 针对自定义平台的额外处理
      if (values.platform === 'custom') {
        apiValues.external_type = values.platformName || 'custom'; // 如果设置了平台名称，使用它作为external_type
        apiValues.external_config.platform_name = values.platformName;

        // 如果填写了自定义请求头，解析并添加
        if (values.headers) {
          try {
            apiValues.external_config.headers = JSON.parse(values.headers);
          } catch(err) {
            message.warning('自定义请求头解析失败，将被忽略');
            console.error('自定义请求头解析失败:', err);
          }
        }
      }

      console.log('准备提交的导入数据:', apiValues);

      // 判断是创建还是更新
      if (selectedRole && selectedRole.source === 'external') {
        // 更新外部角色
        await roleAPI.update(selectedRole.id, apiValues);
        console.log('外部角色更新成功');
        message.success('外部智能体更新成功');
      } else {
        // 创建新的外部角色
        const newRole = await roleAPI.create(apiValues);
        console.log('外部角色创建成功:', newRole);
        message.success('外部智能体导入成功');
      }

      setImportModalVisible(false);
      // 重置表单和状态
      importForm.resetFields();
      setImportPlatform(null);
      setSelectedRole(null);
      // 清除测试连接结果
      setTestConnectionLoading(false);
      setTestConnectionResult(null);
      fetchRoles(); // 重新获取数据
    } catch (error) {
      console.error('表单验证或提交失败:', error);

      // 如果是表单验证错误，显示具体的验证失败信息
      if (error.errorFields && error.errorFields.length > 0) {
        const firstError = error.errorFields[0];
        const fieldName = firstError.name[0];
        const errorMessage = firstError.errors[0];
        message.error(`${fieldName}: ${errorMessage}`);
      } else {
        message.error('导入失败: ' + (error.message || '未知错误'));
      }
    }
  };

  // 添加测试连接状态
  const [testConnectionLoading, setTestConnectionLoading] = useState(false);
  const [testConnectionResult, setTestConnectionResult] = useState(null);

  const handleTestConnection = async () => {
    try {
      // 根据平台类型验证不同的字段
      let fieldsToValidate = ['platform'];

      const platform = importForm.getFieldValue('platform');
      if (platform === 'openai') {
        fieldsToValidate.push('apiKey', 'assistantId');
      } else if (platform === 'dify') {
        fieldsToValidate.push('apiKey', 'apiServer', 'applicationType');
      } else if (platform === 'coze') {
        fieldsToValidate.push('apiKey', 'botId');
      } else if (platform === 'custom') {
        fieldsToValidate.push('apiKey', 'apiServer', 'platformName');
      }

      const values = await importForm.validateFields(fieldsToValidate);

      // 获取所有表单值，包括高级配置中的响应模式和超时设置
      const allValues = importForm.getFieldsValue();
      const testData = {
        ...values,
        responseMode: allValues.responseMode || (globalSettings.streamingEnabled ? 'streaming' : 'blocking'),
        timeout: allValues.timeout || 60,
        userIdentifier: allValues.userIdentifier || ''
      };

      console.log('测试连接数据:', testData);

      // 开始测试连接
      setTestConnectionLoading(true);
      setTestConnectionResult(null);

      try {
        // 检查是否为流式响应模式
        if (testData.responseMode === 'streaming') {
          // 流式响应处理
          await handleStreamingTestConnection(testData);
        } else {
          // 阻塞响应处理
          const response = await roleAPI.testExternalConnection(testData);

          setTestConnectionLoading(false);
          setTestConnectionResult(response);

          if (response.success) {
            message.success(response.message || '连接测试成功');
          } else {
            message.error(response.error || '连接测试失败');
          }
        }
      } catch (error) {
        setTestConnectionLoading(false);
        setTestConnectionResult({
          success: false,
          error: error.message || '网络错误',
          platform: platform
        });
        console.error('连接测试失败:', error);
        message.error('连接测试失败: ' + (error.message || '网络错误'));
      }
    } catch (error) {
      message.error('请填写必要的连接信息');
    }
  };

  // 处理流式测试连接
  const handleStreamingTestConnection = async (testData) => {
    try {
      // 初始化结果状态
      setTestConnectionResult({
        success: false,
        message: '正在连接...',
        test_output: '',
        streaming: true
      });

      // 使用fetch进行流式请求
      const response = await fetch('http://localhost:8080/api/roles/test-external-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let collectedContent = '';

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          break;
        }

        // 解码数据
        buffer += decoder.decode(value, { stream: true });

        // 处理完整的行
        const lines = buffer.split('\n');
        buffer = lines.pop(); // 保留不完整的行

        for (const line of lines) {
          if (line.trim() === '') continue;

          // 解析SSE格式的数据
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              console.log('收到流式数据:', data);

              if (data.error) {
                // 错误处理
                setTestConnectionLoading(false);
                setTestConnectionResult({
                  success: false,
                  error: data.error,
                  test_output: collectedContent,
                  platform: 'dify'
                });
                message.error('连接测试失败: ' + data.error);
                return;
              } else if (data.status === 'connected') {
                // 连接成功，立即更新界面
                setTestConnectionResult({
                  success: false, // 还在进行中
                  message: data.message,
                  test_output: '',
                  streaming: true,
                  platform: 'dify'
                });
              } else if (data.type === 'content') {
                // 内容块，立即更新界面显示
                collectedContent += data.content;
                setTestConnectionResult(prev => ({
                  ...prev,
                  test_output: prev.test_output + data.content,
                  streaming: true
                }));
              } else if (data.type === 'done') {
                // 完成，更新最终状态
                setTestConnectionLoading(false);
                setTestConnectionResult({
                  success: true,
                  message: '流式连接测试成功',
                  test_output: collectedContent,
                  streaming: false,
                  platform: 'dify',
                  test_input: '你好！请简单介绍一下你自己，这是一个连接测试。'
                });
                message.success('流式连接测试成功');
                return;
              }
            } catch (e) {
              console.error('解析流式数据失败:', e, line);
            }
          }
        }
      }

      // 如果到这里还没有设置完成状态，说明流结束了
      setTestConnectionLoading(false);
      if (collectedContent) {
        setTestConnectionResult({
          success: true,
          message: '流式连接测试完成',
          test_output: collectedContent,
          streaming: false
        });
        message.success('流式连接测试完成');
      } else {
        setTestConnectionResult({
          success: false,
          error: '未收到响应内容',
          test_output: ''
        });
        message.error('未收到响应内容');
      }

    } catch (error) {
      console.error('流式连接测试失败:', error);
      setTestConnectionLoading(false);
      setTestConnectionResult({
        success: false,
        error: error.message || '流式连接测试失败',
        test_output: ''
      });
      message.error('流式连接测试失败: ' + (error.message || '网络错误'));
    }
  };

  // 根据选择的平台类型渲染不同的表单字段
  const renderPlatformFields = () => {
    switch (importPlatform) {
      case 'openai':
        return (
          <>
            <Form.Item
              name="apiKey"
              label="API密钥"
              rules={[{ required: true, message: '请输入OpenAI API密钥' }]}
            >
              <Input.Password placeholder="sk-..." />
            </Form.Item>

            <Form.Item
              name="assistantId"
              label="Assistant ID"
              rules={[{ required: true, message: '请输入OpenAI Assistant ID' }]}
            >
              <Input placeholder="asst_..." />
            </Form.Item>

            <Form.Item
              name="model"
              label="模型"
              rules={[{ required: true, message: '请选择使用的模型' }]}
            >
              <Select placeholder="请选择模型">
                <Option value="gpt-4">GPT-4</Option>
                <Option value="gpt-4-turbo">GPT-4 Turbo</Option>
                <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="instructions"
              label="指令集"
            >
              <TextArea rows={4} placeholder="可选，Assistant的系统指令" />
            </Form.Item>
          </>
        );

      case 'dify':
        return (
          <>
            <Form.Item
              name="apiServer"
              label="API服务器地址"
              rules={[
                { required: true, message: '请输入Dify API服务器地址' },
                {
                  pattern: /^https?:\/\//,
                  message: '请输入完整的URL地址，必须以http://或https://开头'
                }
              ]}
              extra="请输入完整的API地址，必须以http://或https://开头"
            >
              <Input placeholder="https://cloud.dify.ai/v1"/>
            </Form.Item>

            <Form.Item
              name="apiKey"
              label="API密钥"
              rules={[{ required: true, message: '请输入Dify应用的API密钥' }]}
            >
              <Input.Password placeholder="app-..." />
            </Form.Item>

            <Form.Item
              name="applicationType"
              label="应用类型"
              rules={[{ required: true, message: '请选择Dify应用类型' }]}
            >
              <Select placeholder="请选择应用类型">
                <Option value="chatbot">Chatbot - 对话助手</Option>
                <Option value="text_generator">Text Generator - 文本生成</Option>
                <Option value="agent">Agent - 智能助手</Option>
                <Option value="chatflow">Chatflow - 对话流</Option>
                <Option value="workflow">Workflow - 工作流</Option>
              </Select>
            </Form.Item>


          </>
        );

      case 'coze':
        return (
          <>
            <Form.Item
              name="botId"
              label="Bot ID"
              rules={[{ required: true, message: '请输入Coze平台的Bot ID' }]}
            >
              <Input placeholder="请输入Coze Bot ID" />
            </Form.Item>

            <Form.Item
              name="apiKey"
              label="API密钥"
              rules={[{ required: true, message: '请输入Coze平台的API密钥' }]}
            >
              <Input.Password placeholder="请输入Coze API密钥" />
            </Form.Item>


          </>
        );

      case 'custom':
        return (
          <>
            <Form.Item
              name="platformName"
              label="平台名称"
              rules={[{ required: true, message: '请输入平台名称' }]}
            >
              <Input placeholder="例如：Claude, Gemini, Xinference等" />
            </Form.Item>

            <Form.Item
              name="apiServer"
              label="API服务器地址"
              rules={[{ required: true, message: '请输入API服务器地址' }]}
            >
              <Input placeholder="例如：https://api.example.com/v1" />
            </Form.Item>

            <Form.Item
              name="apiKey"
              label="API密钥"
              rules={[{ required: true, message: '请输入API密钥' }]}
            >
              <Input.Password placeholder="请输入API密钥" />
            </Form.Item>

            <Form.Item
              name="assistantId"
              label="智能体ID"
            >
              <Input placeholder="如平台支持，请输入智能体ID" />
            </Form.Item>

            <Form.Item
              name="model"
              label="模型标识符"
            >
              <Input placeholder="如平台支持，请输入模型标识符" />
            </Form.Item>

            <Form.Item
              name="instructions"
              label="系统指令"
            >
              <TextArea rows={4} placeholder="可选，智能体的系统指令" />
            </Form.Item>

            <Form.Item
              name="headers"
              label="自定义请求头"
            >
              <TextArea rows={3} placeholder='可选，JSON格式，例如：{"x-api-key": "value", "Authorization": "Bearer xxx"}' />
            </Form.Item>
          </>
        );

      default:
        return null;
    }
  };

  // 渲染能力标签页内容
  const renderCapabilitiesTabContent = () => {
    if (loadingCapabilities) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>正在加载能力数据...</div>
        </div>
      );
    }

    // 能力类型映射
    const typeLabels = {
      'core': '核心能力',
      'advanced': '高级能力',
      'supervision': '监督能力',
      'execution': '执行能力',
      'specialized': '专业能力'
    };

    // 能力图标映射
    const typeIcons = {
      'core': <FunctionOutlined style={{ color: '#1677ff' }} />,
      'advanced': <ThunderboltOutlined style={{ color: '#722ed1' }} />,
      'supervision': <EyeOutlined style={{ color: '#fa8c16' }} />,
      'execution': <CodeOutlined style={{ color: '#eb2f96' }} />,
      'specialized': <AppstoreOutlined style={{ color: '#13c2c2' }} />
    };

    // 如果没有能力数据
    if (Object.keys(capabilities).length === 0) {
      return (
        <Empty
          description="暂无能力数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Text>选择智能体可以使用的能力，这些能力将决定智能体可以执行的操作范围和权限级别。</Text>
        </div>

        {Object.entries(capabilities).map(([type, capList]) => {
          if (!capList || capList.length === 0) return null;

          return (
            <Card
              key={type}
              title={
                <Space>
                  {typeIcons[type] || <AppstoreOutlined />}
                  {typeLabels[type] || type}
                  <Badge count={capList.length} style={{ backgroundColor: '#52c41a' }} />
                </Space>
              }
              variant="borderless"
              style={{ marginBottom: 16 }}
              className="capability-card"
              data-type={type}
            >
              {capList.map(cap => (
                <Form.Item key={cap.id}>
                  <Checkbox
                    checked={!!selectedCapabilities[cap.id]}
                    onChange={(e) => handleCapabilityChange(cap.id, e.target.checked)}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Text strong style={{ marginRight: '8px' }}>{cap.name}</Text>
                      <Tooltip title={cap.description}>
                        <Text
                          type="secondary"
                          style={{
                            maxWidth: '400px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {cap.description}
                        </Text>
                      </Tooltip>
                    </div>
                  </Checkbox>
                </Form.Item>
              ))}
            </Card>
          );
        })}
      </div>
    );
  };

  // 处理能力变更
  const handleCapabilityChange = (capabilityId, checked) => {
    setSelectedCapabilities(prev => ({
      ...prev,
      [capabilityId]: checked
    }));
  };

  // 渲染知识库标签页内容
  const renderKnowledgeTabContent = () => {
    if (loadingKnowledges) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>正在加载知识库数据...</div>
        </div>
      );
    }

    if (allKnowledges.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无可用的知识库"
          >
            <Text type="secondary">
              请先创建内部知识库或配置外部知识库，然后再进行绑定
            </Text>
          </Empty>
        </div>
      );
    }

    return (
      <div style={{ padding: '16px 0' }}>
        <Alert
          message="知识库绑定说明"
          description="选择要绑定到此角色的知识库。智能体在查询时将自动搜索绑定的知识库获取相关信息。支持内部知识库和外部知识库。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form.Item
          label="绑定的知识库"
          tooltip="选择要绑定到此角色的知识库，智能体可以通过MCP服务查询这些知识库"
        >
          <Checkbox.Group
            value={selectedKnowledges}
            onChange={setSelectedKnowledges}
            style={{ width: '100%' }}
          >
            <Row gutter={[16, 16]}>
              {allKnowledges.map(kb => {
                // 为知识库ID添加类型前缀
                const knowledgeId = `${kb.type}_${kb.id}`;
                return (
                <Col span={24} key={knowledgeId}>
                  <Card
                    size="small"
                    style={{
                      border: selectedKnowledges.includes(knowledgeId) ? '2px solid #1677ff' : '1px solid #d9d9d9',
                      borderRadius: 8,
                      transition: 'all 0.3s ease'
                    }}
                    bodyStyle={{ padding: '12px 16px' }}
                  >
                    <Checkbox value={knowledgeId} style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                        <div style={{ flex: 1 }}>
                          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                            <DatabaseOutlined style={{
                              marginRight: 8,
                              color: kb.type === 'internal' ? '#52c41a' : '#1677ff'
                            }} />
                            <Text strong>{kb.name}</Text>
                            <Tag
                              color={kb.type === 'internal' ? 'green' : 'blue'}
                              style={{ marginLeft: 8, fontSize: '10px' }}
                            >
                              {kb.type === 'internal' ? '内部' : '外部'}
                            </Tag>
                            <Badge
                              status={kb.status === 'active' ? 'success' : 'error'}
                              text={kb.status === 'active' ? '正常' : '异常'}
                              style={{ marginLeft: 8 }}
                            />
                          </div>
                          <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>
                            {kb.description || '暂无描述'}
                          </Text>
                          <div style={{ marginTop: 4, display: 'flex', alignItems: 'center' }}>
                            <Text type="secondary" style={{ fontSize: '11px' }}>
                              提供商: {kb.provider_name || '未知'}
                            </Text>
                            {kb.external_id && (
                              <Text type="secondary" style={{ fontSize: '11px', marginLeft: 12 }}>
                                ID: {kb.external_id}
                              </Text>
                            )}
                          </div>
                        </div>
                        <div style={{ marginLeft: 16 }}>
                          {selectedKnowledges.includes(knowledgeId) ? (
                            <LinkOutlined style={{ color: '#52c41a', fontSize: 16 }} />
                          ) : (
                            <DisconnectOutlined style={{ color: '#d9d9d9', fontSize: 16 }} />
                          )}
                        </div>
                      </div>
                    </Checkbox>
                  </Card>
                </Col>
                );
              })}
            </Row>
          </Checkbox.Group>
        </Form.Item>

        {selectedKnowledges.length > 0 && (
          <Alert
            message={`已选择 ${selectedKnowledges.length} 个知识库`}
            description="智能体将能够通过MCP知识库服务查询这些知识库中的信息"
            type="success"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </div>
    );
  };

  // 更新角色的能力关联
  const updateRoleCapabilities = async (roleId, capabilitiesMap) => {
    try {
      // 获取当前角色的能力
      const currentCapabilities = await capabilityAPI.getByRoleId(roleId);
      console.log("当前角色能力数据:", currentCapabilities);

      // 确保currentCapabilities.data是数组
      let currentCapabilityIds = [];
      if (currentCapabilities && currentCapabilities.data) {
        // 检查data是否是数组
        if (Array.isArray(currentCapabilities.data)) {
          currentCapabilityIds = currentCapabilities.data.map(cap => cap.id);
        } else if (typeof currentCapabilities.data === 'object') {
          // 如果data是对象而不是数组，检查是否有status和data字段
          if (currentCapabilities.data.status === 'success' && Array.isArray(currentCapabilities.data.data)) {
            currentCapabilityIds = currentCapabilities.data.data.map(cap => cap.id);
          }
        }
      }

      // 打印日志确认处理后的能力ID
      console.log("处理后的当前能力IDs:", currentCapabilityIds);

      // 要添加的能力IDs
      const selectedCapabilityIds = Object.entries(capabilitiesMap)
        .filter(([_, isSelected]) => isSelected)
        .map(([id]) => parseInt(id));

      console.log("选择的能力IDs:", selectedCapabilityIds);

      // 要移除的能力IDs
      const toRemove = currentCapabilityIds.filter(id => !selectedCapabilityIds.includes(id));

      // 要添加的能力IDs
      const toAdd = selectedCapabilityIds.filter(id => !currentCapabilityIds.includes(id));

      console.log("要添加的能力:", toAdd);
      console.log("要移除的能力:", toRemove);

      // 执行添加
      for (const capId of toAdd) {
        await capabilityAPI.assignToRole(roleId, capId);
      }

      // 执行移除
      for (const capId of toRemove) {
        await capabilityAPI.unassignFromRole(roleId, capId);
      }
    } catch (error) {
      console.error(`更新角色能力关联失败 (角色ID: ${roleId}):`, error);
      throw error;
    }
  };



  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <Title level={4} style={{ margin: 0, marginBottom: '8px' }}>角色管理</Title>
            <Text type="secondary">
              创建和管理智能体角色，配置模型参数、系统提示词和能力权限
            </Text>
          </div>
          <Space>
            <Button
              type="default"
              icon={<CloudOutlined />}
              onClick={showImportModal}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              导入外部智能体
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showAddModal}
              size="large"
              style={{
                borderRadius: '8px',
                height: '42px',
                fontSize: '14px'
              }}
            >
              创建角色
            </Button>
          </Space>
        </div>
      </div>

      <Card
        variant="borderless"
        style={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
        }}
      >
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={{
            current: rolePagination.current,
            pageSize: rolePagination.pageSize,
            defaultPageSize: 10,
            pageSizeOptions: [10, 50, 100],
            showTotal: (total) => `共 ${total} 个角色`,
            showSizeChanger: true,
            showQuickJumper: true,
            size: 'default',
            position: ['bottomRight'],
            simple: false,
            onChange: (page, pageSize) => {
              console.log('当前页码:', page, '每页条数:', pageSize);
              setRolePagination({ current: page, pageSize: pageSize });
            },
            onShowSizeChange: (_, size) => {
              setRolePagination({ current: 1, pageSize: size });
            }
          }}
          style={{ overflowX: 'auto' }}
        />
      </Card>

      <Modal
        title={selectedRole ? '编辑角色' : '创建角色'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        style={{ top: 20 }}
      >
        <Tabs
          activeKey={activeFormTab}
          onChange={setActiveFormTab}
          size="small"
          style={{ marginTop: '20px' }}
        >
          <TabPane
            tab={
              <span>
                <SettingOutlined />
                角色设置
              </span>
            }
            key="roleSettings"
            forceRender
          >
            <Form
              form={form}
              layout="vertical"
            >
              <Form.Item
                name="name"
                label="角色名称"
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>

              <Form.Item
                name="source"
                label="角色类型"
                rules={[{ required: true, message: '请选择角色类型' }]}
                initialValue="internal"
              >
                {selectedRole ? (
                  // 编辑模式下，显示只读的类型标签
                  <div>
                    <Tag color={selectedRole.source === 'external' ? 'green' : 'blue'}>
                      {selectedRole.source === 'external' ? '外部' : '内部'}
                    </Tag>
                    {/* 隐藏字段，保持表单数据完整性 */}
                    <Input type="hidden" value={selectedRole.source || 'internal'} />
                  </div>
                ) : (
                  // 新建模式下，固化为内部类型
                  <div>
                    <Tag color="blue">内部</Tag>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      新建角色默认为内部类型，如需创建外部角色请使用"导入外部智能体"功能
                    </Text>
                    {/* 隐藏字段，保持表单数据完整性 */}
                    <Input type="hidden" value="internal" />
                  </div>
                )}
              </Form.Item>

              <Form.Item
                name="model"
                label="使用的模型"
                rules={[
                  {
                    validator: (_, value) => {
                      if (value === undefined) {
                        return Promise.reject(new Error('请选择使用的模型'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <Select placeholder="请选择使用的模型" loading={loadingModels}>
                  {/* 默认选项 */}
                  <Option key="default" value={null}>
                    默认文本生成 {(() => {
                      const defaultModel = models.find(m => m.is_default_text) || models.find(m => m.is_default);
                      return defaultModel ? `(${defaultModel.name})` : '';
                    })()}
                  </Option>
                  {/* 具体模型选项 */}
                  {models.map(model => (
                    <Option key={model.id} value={model.id}>
                      {model.name} ({model.model_id})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="description"
                label="描述"
                rules={[{ required: true, message: '请输入描述' }]}
              >
                <TextArea rows={2} placeholder="请简要描述该角色的功能和特点" />
              </Form.Item>

              <Form.Item
                name="systemPrompt"
                label={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                    <span>系统提示词</span>
                    <Button
                      type="link"
                      icon={<RobotOutlined />}
                      onClick={handleAssistantGenerate}
                      loading={assistantGenerating}
                      disabled={!globalSettings.enableAssistantGeneration}
                      size="small"
                      style={{
                        color: '#1677ff',
                        fontSize: '12px',
                        padding: '0 4px',
                        height: 'auto'
                      }}
                    >
                      辅助生成
                    </Button>
                  </div>
                }
                rules={[{ required: true, message: '请输入系统提示词' }]}
                extra={
                  !globalSettings.enableAssistantGeneration ?
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      辅助生成功能未启用，请在系统设置中开启
                    </Text> :
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      点击"辅助生成"可根据角色名称和描述自动生成系统提示词
                    </Text>
                }
              >
                <TextArea
                  rows={6}
                  placeholder="请输入详细的系统提示词，用于定义角色的行为和回答风格"
                  style={{
                    backgroundColor: assistantGenerating ? '#f6ffed' : undefined,
                    borderColor: assistantGenerating ? '#b7eb8f' : undefined
                  }}
                />
              </Form.Item>

              <Divider>高级参数设置</Divider>

              <Collapse ghost items={[
                {
                  key: "1",
                  label: "模型参数",
                  children: (
                    <>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            name="temperature"
                            label={
                              <Space>
                                <span>Temperature</span>
                                <Tooltip title="控制生成文本的随机性，越高越有创意但可能偏离主题，越低越保守但可能重复">
                                  <QuestionCircleOutlined />
                                </Tooltip>
                              </Space>
                            }
                            rules={[
                              { required: true, message: '请设置温度值' },
                              { type: 'number', min: 0, max: 2, message: '温度值范围为0-2' }
                            ]}
                            initialValue={0.7}
                          >
                            <InputNumber
                              min={0}
                              max={2}
                              step={0.1}
                              placeholder="0.7"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="topP"
                            label={
                              <Space>
                                <span>Top P</span>
                                <Tooltip title="控制生成文本的多样性，限制可能生成的token数量">
                                  <QuestionCircleOutlined />
                                </Tooltip>
                              </Space>
                            }
                            rules={[
                              { required: true, message: '请设置Top P值' },
                              { type: 'number', min: 0, max: 1, message: 'Top P值范围为0-1' }
                            ]}
                            initialValue={1}
                          >
                            <InputNumber
                              min={0}
                              max={1}
                              step={0.1}
                              placeholder="1.0"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            name="frequencyPenalty"
                            label={
                              <Space>
                                <span>频率惩罚</span>
                                <Tooltip title="减少重复使用相同词语的可能性">
                                  <QuestionCircleOutlined />
                                </Tooltip>
                              </Space>
                            }
                            rules={[
                              { required: true, message: '请设置频率惩罚值' },
                              { type: 'number', min: -2, max: 2, message: '频率惩罚值范围为-2到2' }
                            ]}
                            initialValue={0}
                          >
                            <InputNumber
                              min={-2}
                              max={2}
                              step={0.1}
                              placeholder="0.0"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="presencePenalty"
                            label={
                              <Space>
                                <span>存在惩罚</span>
                                <Tooltip title="减少讨论相同主题的可能性">
                                  <QuestionCircleOutlined />
                                </Tooltip>
                              </Space>
                            }
                            rules={[
                              { required: true, message: '请设置存在惩罚值' },
                              { type: 'number', min: -2, max: 2, message: '存在惩罚值范围为-2到2' }
                            ]}
                            initialValue={0}
                          >
                            <InputNumber
                              min={-2}
                              max={2}
                              step={0.1}
                              placeholder="0.0"
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                      </Row>


                    </>
                  )
                }
              ]} />

              <Divider>测试角色</Divider>

              <Form.Item>
                <Card
                  title="测试角色响应"
                  size="small"
                  style={{ marginBottom: 16 }}
                  extra={
                    <Button
                      type="primary"
                      onClick={handleTestLLM}
                    >
                      测试
                    </Button>
                  }
                >
                  {/* 显示当前角色的系统提示词 */}
                  <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.systemPrompt !== currentValues.systemPrompt}>
                    {({ getFieldValue }) => {
                      const currentSystemPrompt = getFieldValue('systemPrompt');
                      return currentSystemPrompt ? (
                        <div style={{ marginBottom: 12 }}>
                          <div style={{ marginBottom: 8, fontWeight: 500, color: '#333' }}>
                            当前系统提示词:
                          </div>
                          <div style={{
                            border: '1px solid #e8e8e8',
                            padding: 12,
                            borderRadius: 6,
                            background: '#fafafa',
                            fontSize: '13px',
                            color: '#666',
                            maxHeight: 120,
                            overflowY: 'auto',
                            whiteSpace: 'pre-wrap',
                            wordBreak: 'break-word'
                          }}>
                            {currentSystemPrompt}
                          </div>
                        </div>
                      ) : (
                        <div style={{ marginBottom: 12, color: '#999', fontStyle: 'italic' }}>
                          请先填写系统提示词
                        </div>
                      );
                    }}
                  </Form.Item>

                  <div style={{ marginBottom: 8, color: '#666' }}>
                    测试提示: "请简单地介绍一下你自己。"
                  </div>

                  {testVisible && (
                    <div style={{
                      border: '1px solid #f0f0f0',
                      padding: 16,
                      borderRadius: 8,
                      background: '#f9fafb',
                      minHeight: 100,
                      maxHeight: 300,
                      overflowY: 'auto'
                    }}>
                      <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                        {testResult}
                      </div>
                    </div>
                  )}
                </Card>
              </Form.Item>
            </Form>
          </TabPane>

          <TabPane
            tab={
              <span>
                <FunctionOutlined />
                能力设置
              </span>
            }
            key="capabilities"
            forceRender
          >
            {renderCapabilitiesTabContent()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <DatabaseOutlined />
                知识库绑定
              </span>
            }
            key="knowledge"
            forceRender
          >
            {renderKnowledgeTabContent()}
          </TabPane>

        </Tabs>
      </Modal>

      <Modal
        title={selectedRole && selectedRole.source === 'external' ? '编辑外部智能体' : '导入外部智能体'}
        open={importModalVisible}
        onCancel={handleImportModalCancel}
        onOk={handleImportModalOk}
        width={800}
        style={{ top: 20 }}
        footer={[
          <Button key="cancel" onClick={handleImportModalCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleImportModalOk}>
            {selectedRole && selectedRole.source === 'external' ? '更新' : '导入'}
          </Button>,
        ]}
      >
        <Form
          form={importForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="角色名称"
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input placeholder="请输入角色名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={2} placeholder="请简要描述该角色的功能和特点" />
          </Form.Item>

          <Form.Item
            name="source"
            label="角色类型"
            initialValue="external"
          >
            <div>
              <Tag color="green">外部</Tag>
            </div>
          </Form.Item>

          <Form.Item
            name="platform"
            label="平台类型"
            rules={[{ required: true, message: '请选择平台类型' }]}
          >
            <Select
              placeholder="请选择平台类型"
              onChange={(value) => setImportPlatform(value)}
            >
              <Option value="openai">OpenAI</Option>
              <Option value="dify">Dify</Option>
              <Option value="coze">Coze</Option>
              <Option value="custom">自定义</Option>
            </Select>
          </Form.Item>

          {/* 外部角色能力限制说明 - 仅在新建时显示 */}
          {!selectedRole && (
            <Alert
              message="外部角色能力限制"
              description={
                <div>
                  <p style={{ marginBottom: '8px' }}>外部角色相比内部角色在功能上有以下限制：</p>
                  <ul style={{ marginBottom: '0', paddingLeft: '20px' }}>
                    <li><strong>无法调用本地工具</strong>：无法使用系统内置的工具和能力（如文件操作、数据库查询等）</li>
                    <li><strong>依赖外部平台</strong>：功能完全依赖于所选外部平台的能力和限制</li>
                    <li><strong>网络延迟</strong>：响应速度受网络状况和外部平台性能影响</li>
                    <li><strong>数据隐私</strong>：对话数据会发送到外部平台，请注意数据安全</li>
                    <li><strong>成本考虑</strong>：使用外部平台可能产生额外的API调用费用</li>
                  </ul>
                </div>
              }
              type="warning"
              showIcon
              style={{ marginBottom: '16px' }}
            />
          )}

          {renderPlatformFields()}

          {importPlatform && (
            <>


              {/* 高级配置折叠面板 */}
              <Collapse
                ghost
                style={{ marginTop: '16px' }}
                items={[
                  {
                    key: 'advanced',
                    label: '高级配置',
                    children: (
                      <div>
                        <Form.Item
                          name="timeout"
                          label="超时时间"
                          rules={[{ required: true, message: '请输入超时时间' }]}
                          initialValue={60}
                          extra="API请求超时时间（秒）"
                        >
                          <InputNumber min={1} max={300} placeholder="60" />
                        </Form.Item>

                        <Form.Item
                          name="responseMode"
                          label="响应模式"
                          initialValue={globalSettings.streamingEnabled ? 'streaming' : 'blocking'}
                          extra="选择API响应模式，默认跟随全局流式响应设置"
                        >
                          <Select placeholder="选择响应模式">
                            <Option value="blocking">阻塞模式</Option>
                            <Option value="streaming">流式模式</Option>
                          </Select>
                        </Form.Item>

                        <Form.Item
                          name="userIdentifier"
                          label="用户标识"
                          extra="用于标识API调用用户（可选）"
                        >
                          <Input placeholder="user-123" />
                        </Form.Item>
                      </div>
                    )
                  }
                ]}
              />

              {/* 测试连接区域 */}
              <div style={{ marginTop: '24px', padding: '16px', background: '#fafafa', borderRadius: '6px' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '12px',
                  borderBottom: '1px solid #e8e8e8',
                  paddingBottom: '8px'
                }}>
                  <span style={{ fontWeight: 'bold' }}>连接测试</span>
                  <Button
                    type="primary"
                    size="small"
                    onClick={handleTestConnection}
                    loading={testConnectionLoading}
                  >
                    测试连接
                  </Button>
                </div>

                {/* 测试输入 */}
                <div style={{ marginBottom: '12px' }}>
                  <div style={{ fontSize: '13px', fontWeight: 'bold', marginBottom: '4px' }}>
                    测试输入:
                  </div>
                  <div style={{ color: '#666', fontSize: '12px' }}>
                    {testConnectionResult?.test_input || '你好！请简单介绍一下你自己，这是一个连接测试。'}
                  </div>
                </div>

                {/* 响应内容 */}
                <div>
                  <div style={{ fontSize: '13px', fontWeight: 'bold', marginBottom: '8px' }}>
                    响应内容:
                  </div>
                  <div style={{
                    background: '#fff',
                    padding: '12px',
                    borderRadius: '4px',
                    border: '1px solid #d9d9d9',
                    minHeight: '120px',
                    maxHeight: '200px',
                    overflowY: 'auto',
                    fontFamily: 'monospace',
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap'
                  }}>
                    {testConnectionLoading && !testConnectionResult ? (
                      <div style={{ padding: '10px 0', color: '#666', fontSize: '13px' }}>
                        <div style={{ marginBottom: '8px' }}>
                          <Tag color="blue" size="small">正在测试</Tag>
                          <span style={{ marginLeft: '8px' }}>
                            正在测试连接，请稍候...
                          </span>
                        </div>
                        <div style={{
                          background: '#f0f8ff',
                          padding: '8px',
                          borderRadius: '4px',
                          border: '1px dashed #1890ff',
                          minHeight: '60px',
                          color: '#666'
                        }}>
                          等待响应中...
                        </div>
                      </div>
                    ) : testConnectionResult ? (
                      <div>
                        {testConnectionResult.success || testConnectionResult.streaming ? (
                          <div>
                            <div style={{ marginBottom: '8px' }}>
                              {testConnectionResult.streaming ? (
                                <Tag color="blue" size="small">正在接收</Tag>
                              ) : (
                                <Tag color="green" size="small">连接成功</Tag>
                              )}
                              <span style={{ marginLeft: '8px', color: testConnectionResult.streaming ? '#1890ff' : '#52c41a' }}>
                                {testConnectionResult.message}
                              </span>
                              {testConnectionResult.streaming && testConnectionLoading && (
                                <span style={{ marginLeft: '8px', color: '#666', fontSize: '12px' }}>
                                  (流式响应中...)
                                </span>
                              )}
                            </div>
                            <div style={{ color: '#333' }}>
                              {testConnectionResult.test_output || (testConnectionResult.streaming ? '等待响应内容...' : '')}
                            </div>
                          </div>
                        ) : (
                          <div>
                            <Tag color="red" size="small">连接失败</Tag>
                            <span style={{ marginLeft: '8px', color: '#ff4d4f' }}>
                              {testConnectionResult.error || '连接测试失败'}
                            </span>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div style={{ color: '#999', textAlign: 'center', padding: '20px 0' }}>
                        点击"测试连接"按钮开始测试
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </>
          )}
        </Form>
      </Modal>


    </div>
  );
};

export default RoleManagement;